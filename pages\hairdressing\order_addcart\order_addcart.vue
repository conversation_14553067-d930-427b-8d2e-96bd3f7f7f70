<template>
  <view :style="viewColor">
    <!-- 优惠券弹窗 -->
    <block v-if="coupon.coupon">
      <couponListWindow
        :coupon="coupon"
        @ChangCouponsClose="ChangCouponsClose"
        @ChangCouponsUseState="ChangCouponsUseState"
      ></couponListWindow>
    </block>
    <!-- 组件 -->
    <addcartWindow
      :attr="attr"
      :isShow="1"
      :iSplus="1"
      :destri="1"
      :isCustom="isFooter"
      @myevent="onMyEvent"
      @ChangeAttr="ChangeAttr"
      @goCat="goCat"
      @attrVal="attrVal"
      @iptCartNum="iptCartNum"
      id="product-window"
    ></addcartWindow>
    <!--自定义底部tab栏-->
  </view>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import couponListWindow from '@/components/couponListWindow'
import addcartWindow from '@/components/addcartWindow'
import { getNavigation } from '@/api/public'
import { getCartList, getCartCounts, changeCartNum, cartDel, cartProductAttr } from '@/api/order.js'
import { getCoupons, getShopCoupons } from '@/api/api.js'
import { getProductHot, collectAll } from '@/api/store.js'
import { mapGetters } from 'vuex'
import recommend from '@/components/recommend'
import { configMap } from '@/utils'
import { HTTP_REQUEST_URL } from '@/config/app'
import { toLogin } from '@/libs/login.js'
import { CART_TIME, CART_ID } from '@/config/cache'
import { checkCart } from '@/utils/cartCache'
import Cache from '@/utils/cache'
const app = getApp()
export default {
  props: {
    image: {
      type: String,
      default: '',
    },
  },
  components: {
    recommend,
    couponListWindow,
    addcartWindow,
  },
  data() {
    return {
      domain: HTTP_REQUEST_URL,
      loading: false, //是否加载中
      loadend: false, //是否加载完毕
      loadTitle: '加载更多', //提示语
      isFooter: false,
      cartCount: 0,
      goodsHidden: true,
      footerswitch: true,
      hostProduct: [],
      cartList: {
        valid: [],
        invalid: [],
      },
      isAllSelect: false, //全选
      selectValue: [], //选中的数据
      selectCountPrice: 0.0,
      hotScroll: false,
      hotPage: 1,
      hotLimit: 10,
      //属性是否打开
      coupon: {
        coupon: false,
        list: [],
      },
      // 购物车总数
      cartTotalCount: 0,
      recommend: false,
      productValue: [], //系统属性
      attr: {
        cartAttr: false,
        productAttr: [],
        productSelect: {},
      },
      isOpen: false, //是否打开属性组件
      source: '',
      attrImage: '',
      isCart: true,
      cart_id: '',
      attrValue: '', //已选属性
      uniqueValue: '',
      newVal: {},
      goods: {},
      currSku: '',
      selectedArr: [],
    }
  },
  computed: configMap(
    { hide_mer_status: 1, recommend_switch: 0, navigation: {} },
    mapGetters(['isLogin', 'viewColor']),
  ),
  onReady() {},
  mounted: function () {},
  onLoad: function (options) {},
  onShow: function () {
    if (this.isLogin == true) {
      this.getCartList()
      this.getCartNum()
      this.goodsHidden = true
      this.footerswitch = true
      this.isAllSelect = false //全选
      this.selectValue = [] //选中的数据
      uni.setStorage({
        key: 'invoice_Data',
        data: {},
        success: function () {},
      })
    } else {
      this.recommend = true
      this.getHostProduct()
    }
  },
  methods: {
    getNav() {},
    // 删除
    subDel: function (event) {
      let that = this
      let type_id = []
      this.cartList.valid.forEach((el) => {
        el.list.forEach((goods) => {
          if (goods.check) {
            type_id.push(goods.cart_id)
          }
        })
      })
      if (type_id.length == 0) {
        return that.$util.Tips({
          title: '请选择产品',
        })
      } else {
        cartDel({
          cart_id: type_id,
        })
          .then((res) => {
            that.$util.Tips({
              title: res.message,
              icon: 'success',
            })
            this.getCartList()
            this.getCartNum()
          })
          .catch((err) => {
            return that.$util.Tips({
              title: err,
            })
          })
      }
    },
    // 收藏
    subCollect: function (event) {
      let that = this
      let type_id = []
      this.cartList.valid.forEach((el) => {
        el.list.forEach((goods) => {
          if (goods.check) {
            type_id.push(goods.spu.spu_id)
          }
        })
      })
      if (type_id.length == 0) {
        return that.$util.Tips({
          title: '请选择产品',
        })
      } else {
        collectAll({
          type_id: type_id,
          type: 1,
        })
          .then((res) => {
            return that.$util.Tips({
              title: res.message,
              icon: 'success',
            })
          })
          .catch((err) => {
            return that.$util.Tips({
              title: err,
            })
          })
      }
    },
    onMyEvent: function () {
      this.$set(this.attr, 'cartAttr', false)
      this.$set(this, 'isOpen', false)
    },
    /*
     * 更改商品属性
     */
    changeCart: function (goods, id) {
      let that = this
      if (that.goods == goods) {
        that.isOpen = that.attr.cartAttr = true
        return
      } else {
        cartProductAttr(id)
          .then((res) => {
            goods.attr = res.data.attr
            goods.attrValue = res.data.attrValue
            that.attrValue = goods.productAttr.sku
            that.attrImage = goods.product.image
            that.goods = JSON.parse(JSON.stringify(goods))
            that.currSku = goods.productAttr.sku.split(',')
            that.$set(that.attr, 'productAttr', res.data.attr)
            const sku = {}
            res.data.attrValue.forEach((itemn) => {
              sku[itemn.sku] = itemn
            })
            that.$set(that, 'productValue', sku)
            let productSelect = sku[that.attrValue]
            that.isOpen = that.attr.cartAttr = true
            that.DefaultSelect(goods)
          })
          .catch((err) => {
            return that.$util.Tips({
              title: err,
            })
          })
      }
    },
    /**
     * 默认选中属性
     *
     */
    DefaultSelect: function (goods) {
      let productAttr = this.attr.productAttr
      let value = []
      let arr = []
      if (this.currSku) {
        value = this.currSku
      } else {
        for (var key in this.productValue) {
          if (this.productValue[key].stock > 0) {
            value = this.attr.productAttr.length ? key.split(',') : []
            break
          }
        }
      }
      for (let i = 0; i < productAttr.length; i++) {
        this.$set(productAttr[i], 'index', value[i])
      }
      let productSelect = this.productValue[value.join(',')]
      if (productSelect && productAttr.length) {
        this.$set(this.attr.productSelect, 'store_name', goods.product.store_name)
        this.$set(
          this.attr.productSelect,
          'image',
          productSelect.image ? productSelect.image : goods.product.image,
        )
        this.$set(this.attr.productSelect, 'price', productSelect.price)
        this.$set(this.attr.productSelect, 'stock', productSelect.stock)
        this.$set(this.attr.productSelect, 'unique', productSelect.unique)
        this.$set(this.attr.productSelect, 'svip_price', productSelect.svip_price)
        this.$set(this, 'uniqueValue', productSelect.unique)
        this.$set(this, 'attrValue', value.join(','))
        this.$set(this, 'attrTxt', '已选择')
        if (productSelect.stock == 0) {
          this.$set(this.attr.productSelect, 'cart_num', 0)
        } else {
          this.$set(this.attr.productSelect, 'cart_num', 1)
        }
      } else if (!productSelect && productAttr.length) {
        this.$set(this.attr.productSelect, 'store_name', goods.product.store_name)
        this.$set(this.attr.productSelect, 'image', goods.product.image)
        this.$set(this.attr.productSelect, 'price', goods.product.price)
        this.$set(this.attr.productSelect, 'svip_price', productSelect.svip_price)
        this.$set(this.attr.productSelect, 'stock', 0)
        this.$set(this.attr.productSelect, 'unique', '')
        this.$set(this, 'uniqueValue', '')
        this.$set(this.attr.productSelect, 'cart_num', 0)
        this.$set(this, 'attrValue', '')
        this.$set(this, 'attrTxt', '请选择')
      } else if (!productSelect && !productAttr.length) {
        this.$set(this.attr.productSelect, 'store_name', goods.product.store_name)
        this.$set(this.attr.productSelect, 'image', goods.product.image)
        this.$set(this.attr.productSelect, 'price', goods.product.price)
        this.$set(this.attr.productSelect, 'svip_price', productSelect.svip_price)
        this.$set(this.attr.productSelect, 'stock', goods.product.stock)
        this.$set(this.attr.productSelect, 'unique', goods.product.unique || '')
        this.$set(this, 'uniqueValue', goods.product.unique || '')
        this.$set(this.attr.productSelect, 'cart_num', 1)
        this.$set(this, 'attrValue', '')
        this.$set(this, 'attrTxt', '请选择')
      } else if (productSelect && !productAttr.length) {
        this.$set(this.attr.productSelect, 'store_name', goods.product.store_name)
        this.$set(this.attr.productSelect, 'image', productSelect.image)
        this.$set(this.attr.productSelect, 'price', productSelect.price)
        this.$set(this.attr.productSelect, 'svip_price', productSelect.svip_price)
        this.$set(this.attr.productSelect, 'stock', productSelect.stock)
        this.$set(this.attr.productSelect, 'unique', productSelect.unique)
        this.$set(this, 'uniqueValue', productSelect.unique)
        this.$set(this, 'attrValue', value.join(','))
        this.$set(this, 'attrTxt', '已选择')
        if (productSelect.stock == 0) {
          this.$set(this.attr.productSelect, 'cart_num', 0)
        } else {
          this.$set(this.attr.productSelect, 'cart_num', 1)
        }
      }
      this.goCart(productSelect)
    },
    goCart(productSelect) {
      let that = this
      //如果有属性,没有选择,提示用户选择
      if (that.attr.productAttr.length && that.isOpen === true && productSelect.stock == 0)
        return that.$util.Tips({
          title: '产品库存不足，请选择其它',
        })
      if (that.attr.productSelect.cart_num == 0) {
        return that.$util.Tips({
          title: '购买个数不能为0！',
        })
      }
    },
    attrVal(val) {
      this.$set(
        this.attr.productAttr[val.indexw],
        'index',
        this.attr.productAttr[val.indexw].attr_values[val.indexn],
      )
    },
    /**
     * 购物车手动填写
     *
     */
    iptCartNum: function (e) {
      this.$set(this.attr.productSelect, 'cart_num', e)
    },
    /**
     * 属性变动赋值
     *
     */
    ChangeAttr: function (res) {
      let productSelect = this.productValue[res]
      this.currSku = res
      this.newVal = this.productValue[res]
      this.attr.productSelect = { ...this.attr.productSelect, ...productSelect }
      if (!productSelect || productSelect.stock <= 0) {
        this.$set(this.attr.productSelect, 'stock', 0)
        this.$set(this.attr.productSelect, 'unique', '')
        this.$set(this.attr.productSelect, 'cart_num', 0)
      }
    },
    // 立即下单
    subOrder: function (event) {
      let selectValue = []
      this.cartList.valid.forEach((el) => {
        el.list.forEach((goods) => {
          if (goods.check) {
            selectValue.push(goods.cart_id)
          }
        })
      })
      if (selectValue.length > 0) {
        uni.navigateTo({
          url: '/pages/users/order_confirm/index?cartId=' + selectValue.join(','),
        })
      } else {
        return this.$util.Tips({
          title: '请选择产品',
        })
      }
    },
    // 购物车增加
    addCart: function (goods, index) {
      if (
        (goods.hasOwnProperty('productAttr') && goods.cart_num >= goods.productAttr.stock) ||
        (goods.product.once_max_count > 0 &&
          goods.product.once_min_count > 0 &&
          goods.cart_num >= goods.product.once_max_count)
      ) {
        if (goods.hasOwnProperty('productAttr') && goods.cart_num >= goods.productAttr.stock)
          goods.cart_num = goods.productAttr.stock
        goods.numAdd = true
        goods.numSub = false
        return
      }
      let that = this
      changeCartNum(goods.cart_id, {
        cart_num: goods.cart_num + 1,
      })
        .then((res) => {
          goods.cart_num = Number(goods.cart_num) + 1
          that.cartTotalCount = Number(that.cartTotalCount) + 1
          goods.numAdd = false
          goods.numSub = false
          this.cartAllCheck('goodsCheck')
          this.getCartNum()
        })
        .catch((error) => {
          that.$util.Tips({
            title: error,
          })
        })
    },
    goCat: function () {
      let that = this
      changeCartNum(that.goods.cart_id, {
        cart_num: that.goods.cart_num,
        product_attr_unique: that.newVal.unique,
      })
        .then((res) => {
          if (
            that.goods.hasOwnProperty('productAttr') &&
            that.goods.cart_num > that.goods.productAttr.stock
          ) {
            that.goods.cart_num = that.goods.productAttr.stock
            that.goods.numAdd = true
            that.goods.numSub = false
            return
          } else {
            that.goods.numAdd = false
            that.goods.numSub = false
          }
          that.onMyEvent()
          that.getCartList(true)
          that.isAllSelect = false
        })
        .catch((error) => {
          that.$util.Tips({
            title: error,
          })
        })
    },
    // 购物车递减
    subCart(goods) {
      let status = false
      if (
        goods.cart_num < 1 ||
        (goods.product.once_max_count > 0 &&
          goods.product.once_min_count > 0 &&
          goods.cart_num <= goods.product.once_min_count)
      )
        status = true
      if (goods.cart_num <= 1) {
        goods.cart_num = 1
        goods.numSub = true
        status = true
      } else {
        if (false == status) {
          changeCartNum(goods.cart_id, {
            cart_num: Number(goods.cart_num) - 1,
          })
            .then((res) => {
              goods.numSub = false
              goods.numAdd = false
              if (goods.cart_num <= 1) {
                goods.numSub = true
              }
              goods.cart_num = Number(goods.cart_num) - 1
              this.cartTotalCount = Number(this.cartTotalCount) - 1
              this.cartAllCheck('goodsCheck')
              this.getCartNum()
            })
            .catch((error) => {
              this.$util.Tips({
                title: error,
              })
            })
        }
      }
    },
    getCartNum: function () {
      let that = this
      that.$util.getCartNum(that.isLogin, function (count) {
        that.cartTotalCount = count
      })
    },
    // 购物车列表
    getCartList: function (isChange) {
      let that = this
      getCartList().then((res) => {
        if (checkCart()) {
          that.selectedArr = (Cache.get(CART_ID) && JSON.parse(Cache.get(CART_ID))) || []
        } else {
          that.selectedArr = []
        }
        res.data.list.forEach((item, index) => {
          item.allCheck = false
          item.list.forEach((goods, j) => {
            goods.check = that.selectedArr.indexOf(goods.cart_id) != -1
            if (goods.cart_num == 1) {
              goods.numSub = true
            } else {
              goods.numSub = false
            }
            if (goods.cart_num == goods.productAttr.stock) {
              goods.numAdd = true
            } else {
              goods.numAdd = false
            }
          })
        })
        this.cartList.valid = res.data.list
        this.cartList.invalid = res.data.fail
        if (res.data.list.length == 0) {
          this.recommend = true
          if (!this.hostProduct.length) {
            this.getHostProduct()
          }
        } else {
          this.recommend = false
        }
        const sku = {}
        this.cartAllCheck('goodsCheck')
        uni.stopPullDownRefresh() //结束下拉刷新
      })
    },
    // 商铺全选
    storeAllCheck(item, index) {
      let cart_id = (Cache.get(CART_ID) && JSON.parse(Cache.get(CART_ID))) || []
      // 店铺取消
      if (item.allCheck) {
        item.allCheck = false
        item.list.forEach((el, index) => {
          el.check = false
          cart_id.forEach((id, i) => {
            if (id == el.cart_id) cart_id.splice(i, 1)
          })
        })
      } else {
        item.allCheck = true
        item.list.forEach((el, index) => {
          el.check = true
          if (cart_id.indexOf(el.cart_id) == -1) cart_id.push(el.cart_id)
        })
      }
      Cache.set(CART_ID, Array.from([...new Set(cart_id)]))
      const timestamp = Date.now()
      Cache.set(CART_TIME, timestamp)
      this.cartAllCheck('goodsCheck')
    },
    // 商品选中
    goodsCheck(goods) {
      goods.check = !goods.check
      let arr = (Cache.get(CART_ID) && JSON.parse(Cache.get(CART_ID))) || []
      if (goods.check) {
        arr.push(goods.cart_id)
        Cache.set(CART_ID, Array.from([...new Set(arr)]))
      } else {
        let new_set = new Set(arr)
        new_set.delete(goods.cart_id)
        Cache.set(CART_ID, Array.from([...new_set]))
      }
      const timestamp = Date.now()
      Cache.set(CART_TIME, timestamp)
      this.cartAllCheck('goodsCheck')
    },
    // 购物车全选
    checkboxAllChange() {
      let that = this
      that.isAllSelect = !that.isAllSelect
      if (!that.isAllSelect) {
        Cache.set(CART_ID, [])
      } else {
        let arr = (Cache.get('CART_ID') && JSON.parse(Cache.get('CART_ID'))) || []
        that.cartList.valid.forEach((el, index) => {
          el.list.forEach((e) => {
            arr.push(e.cart_id)
          })
        })
        Cache.set(CART_ID, Array.from([...new Set(arr)]))
        const timestamp = Date.now()
        Cache.set(CART_TIME, timestamp)
      }
      this.cartAllCheck('cartCheck')
    },
    // 全选判断
    cartAllCheck(type) {
      let that = this
      let allArr = []
      let totalMoney = 0
      let totalNum = 0
      that.cartList.valid.forEach((el, index) => {
        if (type == 'goodsCheck') {
          let tempArr = el.list.filter((goods) => {
            return goods.check == true
          })
          if (el.list.length == tempArr.length) {
            el.allCheck = true
            allArr.push(el)
          } else {
            el.allCheck = false
          }
        } else {
          el.list.forEach((goods) => {
            goods.check = that.isAllSelect
          })
          el.allCheck = that.isAllSelect
          if (el.allCheck) allArr.push(el)
        }
        // 总金额 //总数
        el.list.forEach((e) => {
          if (e.check) {
            totalMoney = that.$util.$h.Add(
              totalMoney,
              that.$util.$h.Mul(e.productAttr.price, e.cart_num),
            )
            totalNum += e.cart_num
          }
        })
      })
      that.cartCount = totalNum
      that.selectCountPrice = totalMoney
      // 全选
      that.isAllSelect = allArr.length == that.cartList.valid.length ? true : false
    },
    // 推荐列表
    getHostProduct: function () {
      let that = this
      if (that.loadend) return
      if (that.hotScroll) return
      that.loading = true
      that.loadTitle = '加载更多'
      getProductHot(that.hotPage, that.hotLimit).then((res) => {
        let list = res.data.list || []
        that.hotPage++
        that.hotScroll = res.data.list.length < that.hotLimit
        that.hostProduct = that.hostProduct.concat(res.data.list)
        that.loading = false
        that.loadTitle = that.hotScroll ? '我也是有底线的' : '加载更多'
      })
    },
    // 失效商品展开
    goodsOpen: function () {
      let that = this
      that.goodsHidden = !that.goodsHidden
    },
    // 管理
    manage: function () {
      let that = this
      that.footerswitch = !that.footerswitch
    },
    // 清空
    unsetCart: function () {
      let that = this,
        ids = []
      for (let i = 0, len = that.cartList.invalid.length; i < len; i++) {
        ids.push(that.cartList.invalid[i].cart_id)
      }
      cartDel({
        cart_id: ids,
      })
        .then((res) => {
          that.$util.Tips({
            title: '清除成功',
          })
          that.getCartNum()
          that.$set(that.cartList, 'invalid', [])
        })
        .catch((res) => {})
    },
    // 店铺优惠券
    giveCoupon(item) {
      let that = this
      let goodsArr = []
      let couponList = []
      let activeList = []
      let ids = []
      item.list.map((el) => {
        ids.push(el.product_id)
      })
      uni.showLoading({
        title: '加载中...',
        mask: true,
      })
      getCoupons({
        ids: ids.join(','),
      }).then((res) => {
        goodsArr = res.data
        getShopCoupons(item.mer_id)
          .then(({ data }) => {
            uni.hideLoading()
            couponList = goodsArr.concat(data)
            this.$set(this.coupon, 'list', couponList)
            this.$set(this.coupon, 'coupon', true)
          })
          .catch((error) => {})
      })
    },
    ChangCouponsClose: function () {
      this.$set(this.coupon, 'coupon', false)
    },
    ChangCouponsUseState(index) {
      let that = this
      that.coupon.list[index].issue = true
    },
  },
  onPullDownRefresh: function () {
    this.cartList = {
      valid: [],
      invalid: [],
    }
    this.getCartNum()
    this.getCartList()
  },
  onReachBottom() {
    this.recommend && this.getHostProduct()
  },
  // 滚动监听
  onPageScroll(e) {
    // 传入scrollTop值并触发所有easy-loadimage组件下的滚动监听事件
    uni.$emit('scroll')
  },
}
</script>

<style scoped lang="scss">
.shoppingCart .labelNav {
  height: 76rpx;
  padding: 0 30rpx;
  font-size: 22rpx;
  color: #8c8c8c;
  position: fixed;
  left: 0;
  width: 100%;
  box-sizing: border-box;
  background-color: #f5f5f5;
  z-index: 5;
  top: 0;
}
.shoppingCart .labelNav .item .iconfont {
  font-size: 25rpx;
  margin-right: 10rpx;
}
.t-color {
  color: var(--view-theme);
}
.shoppingCart {
  /* #ifndef MP */
  padding-bottom: 120rpx;
  /* #endif */
  /* #ifdef MP */
  padding-bottom: calc(120rpx+ constant(safe-area-inset-bottom));
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  /* #endif */
}
.shoppingCart .nav-head {
  background: #f5f5f5;
  width: 100%;
  height: 80rpx;
  position: fixed;
  left: 0;
  z-index: 5;
  top: 76rpx;
}
.shoppingCart .nav {
  width: 710rpx;
  height: 80rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  margin-left: 20rpx;
  border-radius: 16rpx;
}
.shoppingCart .nav .administrate {
  font-size: 26rpx;
  color: #282828;
  width: 110rpx;
  height: 46rpx;
  border-radius: 24rpx;
  border: 1px solid #ddd;
}
.shoppingCart .noCart {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  padding-top: 0.1rpx;
}
.shoppingCart .noCart .pictrue {
  text-align: center;
  margin: 0 auto;
  padding: 80rpx 0 100rpx;
}
.shoppingCart .noCart .pictrue image {
  width: 414rpx;
  height: 305rpx;
  border-radius: 16rpx;
}
.shoppingCart .noCart .pictrue view {
  color: #999;
}
.shoppingCart .list {
  margin: 171rpx 0 20rpx;
}
.shoppingCart .list .item {
  margin-bottom: 20rpx;
  .store-title {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 30rpx;
    height: 85rpx;
    .checkbox {
      width: 60rpx;
      .iconfont {
        font-size: 40rpx;
        color: #cccccc;
      }
      .icon-a-ic_CompleteSelect {
        color: var(--view-theme);
      }
    }
    .info {
      flex: 1;
      display: flex;
      align-items: center;
      .iconfont {
        font-size: 36rpx;
      }
      .name {
        margin: 0 0 0 10rpx;
        font-size: 28rpx;
        color: #282828;
        font-weight: 500;
      }
      .icon-ic_rightarrow {
        margin-top: 6rpx;
        font-size: 28rpx;
      }
    }
    .coupon-btn {
      color: var(--view-theme);
      font-size: 22rpx;
      width: 100rpx;
      line-height: 36rpx;
      background: var(--view-minorColor);
      border-radius: 18rpx;
      text-align: center;
    }
  }
}
.vipImg {
  width: 65rpx;
  height: 28rpx;
  margin-left: 10rpx;
  image {
    width: 100%;
    height: 100%;
    display: block;
  }
}
.shoppingCart .list .item .picTxt {
  width: 100%;
  padding: 25rpx 30rpx;
  position: relative;
  align-items: center;
  .checkbox {
    width: 55rpx;
    .iconfont {
      font-size: 40rpx;
      color: #cccccc;
    }
    .icon-a-ic_CompleteSelect {
      color: var(--view-theme);
    }
  }
}
.shoppingCart .list .item .item-right {
  width: 590rpx;
}
.shoppingCart .list .item .picTxt .pictrue {
  width: 180rpx;
  height: 180rpx;
}
.shoppingCart .list .item .picTxt .pictrue image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}
.shoppingCart .list .item .picTxt .text {
  width: 390rpx;
  font-size: 28rpx;
  color: #282828;
}
.shoppingCart .list .item .picTxt .buy_limit {
  margin-top: 10rpx;
  color: var(--view-theme);
  font-size: 22rpx;
}
.shoppingCart .list .item .picTxt .text .infor {
  display: inline-block;
  padding: 10rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
  background-color: #f5f5f5;
  max-width: 400rpx;
  .icon-ic_downarrow {
    font-size: 24rpx;
    padding: 0 5rpx;
  }
}
.shoppingCart .list .item .carnum-count {
  margin-top: 28rpx;
}
.shoppingCart .list .item .picTxt .text .money {
  font-size: 32rpx;
  color: var(--view-theme);
}
.shoppingCart .invalidGoods .goodsNav {
  width: 100%;
  height: 66rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #282828;
}
.shoppingCart .invalidGoods .goodsNav .iconfont {
  color: #424242;
  font-size: 28rpx;
  margin-right: 17rpx;
}
.shoppingCart .invalidGoods .goodsNav .del {
  font-size: 26rpx;
  color: #999;
}
.shoppingCart .invalidGoods .goodsNav .del .icon-ic_delete {
  color: #999;
  font-size: 33rpx;
  vertical-align: -2rpx;
  margin-right: 8rpx;
}
.shoppingCart .invalidGoods .goodsList .item {
  padding: 20rpx 30rpx;
  border-top: 1px solid #f5f5f5;
}
.shoppingCart .invalidGoods .goodsList .item .invalid {
  font-size: 22rpx;
  color: #fff;
  width: 75rpx;
  height: 38rpx;
  background-color: #ccc;
  border-radius: 20rpx;
  text-align: center;
  line-height: 36rpx;
}
.shoppingCart .invalidGoods .goodsList .item .pictrue {
  width: 140rpx;
  height: 140rpx;
}
.shoppingCart .invalidGoods .goodsList .item .pictrue image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}
.shoppingCart .invalidGoods .goodsList .item .text {
  width: 400rpx;
  font-size: 28rpx;
  color: #999;
  height: 140rpx;
}
.shoppingCart .invalidGoods .goodsList .item .text .name {
  width: 100%;
}
.shoppingCart .invalidGoods .goodsList .item .text .infor {
  font-size: 24rpx;
}
.shoppingCart .invalidGoods .goodsList .item .text .end {
  font-size: 26rpx;
  color: #bbb;
}
.shoppingCart .footer {
  z-index: 9;
  width: 100%;
  height: 96rpx;
  background-color: #fff;
  position: fixed;
  padding: 0 30rpx;
  box-sizing: border-box;
  border-top: 1px solid #eee;
  left: 0;
  /*#ifdef H5*/
  bottom: 98rpx;
  bottom: calc(98rpx + constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
  bottom: calc(98rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
  /*#endif*/
  /*#ifdef MP || APP-PLUS*/
  bottom: 0;
  /*#endif*/
}
.shoppingCart .footer.on {
  /*#ifdef MP || APP-PLUS*/
  bottom: 98rpx;
  bottom: calc(98rpx + constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
  bottom: calc(98rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
  /*#endif*/
}
.shoppingCart .footer .money {
  font-size: 30rpx;
}
.shoppingCart .footer .placeOrder {
  color: #fff;
  font-size: 26rpx;
  width: 200rpx;
  height: 72rpx;
  border-radius: 60rpx;
  text-align: center;
  line-height: 72rpx;
  margin-left: 22rpx;
  background-color: var(--view-theme);
}
.shoppingCart .footer .button .bnt {
  font-size: 28rpx;
  color: #666;
  border-radius: 50rpx;
  border: 1px solid #ddd;
  width: 160rpx;
  height: 60rpx;
  text-align: center;
  line-height: 60rpx;
  &.bt-color {
    color: var(--view-theme);
    border: 1px solid var(--view-theme);
  }
}
.shoppingCart .footer .button form ~ form {
  margin-left: 17rpx;
}
.allcheckbox {
  display: flex;
  align-items: center;
  width: 260rpx;
  .iconfont {
    margin-right: 20rpx;
    font-size: 40rpx;
    color: #cccccc;
  }
  .icon-a-ic_CompleteSelect {
    color: var(--view-theme);
  }
}
</style>
